import OpenAI from 'openai';
import Store from 'electron-store';
import type { Request, Response } from 'express';

// Use type assertion to bypass electron-store typing issues
const store = new Store() as {
  get: (key: string) => unknown;
  set: (key: string, value: unknown) => void;
};

export const handleChatRequest = async (req: Request, res: Response) => {
  try {
    const { message, context, apiKey } = req.body;
    const storedKey = store.get('openaiApiKey');
    const openaiApiKey = apiKey || (typeof storedKey === 'string' ? storedKey : '');

    if (!openaiApiKey) {
      return res.status(400).json({ error: 'OpenAI API key not configured' });
    }

    const openai = new OpenAI({ apiKey: openaiApiKey });

    const prompt = `The current working directory is: ${context.cwd}
User request: ${message}

Generate shell commands that fulfill this request. For each command:
1. Output only the command itself
2. Include a brief explanation before the command as a comment
3. Only output valid shell commands for the current OS (${process.platform})
4. Do not include any markdown formatting`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2,
      max_tokens: 500,
    });

    const content = response.choices[0]?.message?.content || '';
    const commands = content.split('\n').filter((line: string) => line.trim() && !line.startsWith('#'));
    const explanation = content.split('\n').filter((line: string) => line.startsWith('#')).join('\n');

    res.json({ commands, explanation });
  } catch (error) {
    console.error('Chat request failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  }
};